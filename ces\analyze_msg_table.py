#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析MSG表结构和引用消息的所有字段
"""

import requests
import json

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"

def get_db_handle():
    """获取数据库句柄"""
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={}
        )

        if response.status_code == 200:
            data = response.json()
            if data.get("Code") == 1 and data.get("Data"):
                for db_info in data["Data"]:
                    if "MSG0.db" in db_info.get("databaseName", ""):
                        return str(db_info["handle"])
        return None
    except Exception as e:
        print(f"获取数据库句柄失败: {e}")
        return None

def analyze_msg_table():
    """分析MSG表结构"""
    db_handle = get_db_handle()
    if not db_handle:
        print("❌ 无法获取数据库句柄")
        return

    print(f"✅ 获取到数据库句柄: {db_handle}")

    # 1. 查看MSG表结构
    print("\n📊 MSG表结构:")
    sql_schema = "PRAGMA table_info(MSG);"
    
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql_schema}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                for field_info in result["Data"]:
                    print(f"  {field_info[1]} ({field_info[2]})")  # 字段名和类型
                    
    except Exception as e:
        print(f"查询表结构失败: {e}")

    # 2. 查询最新的引用消息的所有字段
    print("\n📋 最新引用消息的所有字段:")
    sql_all = """
    SELECT *
    FROM MSG 
    WHERE Type = 49 
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId DESC 
    LIMIT 1;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql_all}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                msg_data = result["Data"][0]
                print(f"字段数量: {len(msg_data)}")
                for i, value in enumerate(msg_data):
                    if value and str(value).strip():  # 只显示非空字段
                        print(f"  字段[{i}]: {value}")
                        
    except Exception as e:
        print(f"查询消息数据失败: {e}")

if __name__ == "__main__":
    analyze_msg_table()
