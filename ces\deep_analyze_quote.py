#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析引用消息在数据库中的存储方式
"""

import requests
import json
import base64
import re

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"

def get_db_handle():
    """获取数据库句柄"""
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={}
        )

        if response.status_code == 200:
            data = response.json()
            if data.get("Code") == 1 and data.get("Data"):
                for db_info in data["Data"]:
                    if "MSG0.db" in db_info.get("databaseName", ""):
                        return str(db_info["handle"])
        return None
    except Exception as e:
        print(f"获取数据库句柄失败: {e}")
        return None

def analyze_quote_message_storage():
    """深度分析引用消息的存储方式"""
    db_handle = get_db_handle()
    if not db_handle:
        print("❌ 无法获取数据库句柄")
        return

    print(f"✅ 获取到数据库句柄: {db_handle}")

    # 分析最新的引用消息和对应的标准消息
    target_quote_id = "2702593880357607128"  # 您提供的引用消息ID
    target_original_id = "1432938654565177894"  # 被引用的原始消息ID
    
    print(f"\n🎯 分析目标消息:")
    print(f"引用消息ID: {target_quote_id}")
    print(f"原始消息ID: {target_original_id}")
    print("=" * 50)

    # 1. 查询引用消息的完整信息
    print("\n📋 引用消息完整信息:")
    sql_quote = f"""
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, DisplayContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
    FROM MSG
    WHERE MsgSvrID = '{target_quote_id}'
    LIMIT 1;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql_quote}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                msg_data = result["Data"][0]
                print(f"localId: {msg_data[0]}")
                print(f"StrContent: '{msg_data[4]}' (长度: {len(str(msg_data[4])) if msg_data[4] else 0})")
                print(f"DisplayContent: '{msg_data[5]}' (长度: {len(str(msg_data[5])) if msg_data[5] else 0})")
                print(f"Type: {msg_data[7]}, SubType: {msg_data[8]}")
                print(f"BytesExtra长度: {len(str(msg_data[9])) if msg_data[9] else 0}")
                print(f"CompressContent长度: {len(str(msg_data[10])) if msg_data[10] else 0}")
                
                # 分析CompressContent
                compress_content = msg_data[10]
                if compress_content:
                    try:
                        decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
                        print(f"\n📄 CompressContent解码内容:")
                        print(decoded)
                        print("\n🔍 查找可能的内容字段:")
                        
                        # 查找所有可能包含用户输入的字段
                        patterns = [
                            (r'<title[^>]*><!\[CDATA\[(.*?)\]\]></title>', "title"),
                            (r'<content[^>]*><!\[CDATA\[(.*?)\]\]></content>', "content"),
                            (r'<displayname[^>]*><!\[CDATA\[(.*?)\]\]></displayname>', "displayname"),
                            (r'<digest[^>]*><!\[CDATA\[(.*?)\]\]></digest>', "digest"),
                            (r'<des[^>]*><!\[CDATA\[(.*?)\]\]></des>', "des"),
                        ]
                        
                        found_content = False
                        for pattern, name in patterns:
                            matches = re.findall(pattern, decoded, re.DOTALL)
                            if matches:
                                print(f"  {name}: {matches}")
                                found_content = True
                        
                        if not found_content:
                            print("  ❌ 未找到任何CDATA内容")
                            
                    except Exception as e:
                        print(f"CompressContent解析失败: {e}")
                        
    except Exception as e:
        print(f"查询引用消息失败: {e}")

    # 2. 查询原始消息的完整信息作为对比
    print(f"\n📋 原始消息完整信息 (ID: {target_original_id}):")
    sql_original = f"""
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, DisplayContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
    FROM MSG
    WHERE MsgSvrID = '{target_original_id}'
    LIMIT 1;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql_original}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                msg_data = result["Data"][0]
                print(f"StrContent: '{msg_data[4]}' (长度: {len(str(msg_data[4])) if msg_data[4] else 0})")
                print(f"DisplayContent: '{msg_data[5]}' (长度: {len(str(msg_data[5])) if msg_data[5] else 0})")
                print(f"Type: {msg_data[7]}, SubType: {msg_data[8]}")
                
    except Exception as e:
        print(f"查询原始消息失败: {e}")

    # 3. 查询MSG表的所有字段名
    print(f"\n📊 MSG表结构:")
    sql_schema = "PRAGMA table_info(MSG);"
    
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql_schema}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                print("字段列表:")
                for field_info in result["Data"]:
                    print(f"  {field_info[1]} ({field_info[2]})")
                    
    except Exception as e:
        print(f"查询表结构失败: {e}")

if __name__ == "__main__":
    analyze_quote_message_storage()
