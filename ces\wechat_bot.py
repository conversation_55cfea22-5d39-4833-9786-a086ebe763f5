#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控与Coze智能回复系统
功能：监控微信群消息，调用Coze API进行智能回复
"""

import requests
import json
import time
import datetime
import re
import base64
import logging
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"
COZE_TOKEN = "pat_iIRA1rSJkqdW1jW4NtvDVwQauDJEzGfur2QkYaaXR3kbO0Q5jZKQr7mGfirjvDMi"
COZE_BOT_ID = "7540166794171301940"

class WeChatCozeBot:
    def __init__(self):
        self.last_msg_id = 0
        self.contacts_cache = {}
        self.db_handle = None
        self.is_first_run = True  # 标记是否是第一次运行

    def get_db_handle(self):
        """获取数据库句柄"""
        try:
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={}
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1 and data.get("Data"):
                    # 查找MSG0.db的句柄
                    for db_info in data["Data"]:
                        if "MSG0.db" in db_info.get("databaseName", ""):
                            self.db_handle = str(db_info["handle"])
                            logger.info(f"获取到MSG0.db句柄: {self.db_handle}")
                            return True

                    logger.error("未找到MSG0.db数据库")
                    return False
                else:
                    logger.error(f"获取数据库信息失败: {data}")
                    return False
            else:
                logger.error(f"请求失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"获取数据库句柄异常: {e}")
            return False

    def load_contacts(self):
        """加载联系人信息到缓存"""
        try:
            # 获取MicroMsg.db句柄
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={}
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1 and data.get("Data"):
                    micromsg_handle = None
                    for db_info in data["Data"]:
                        if "MicroMsg.db" in db_info.get("databaseName", ""):
                            micromsg_handle = str(db_info["handle"])
                            break

                    if not micromsg_handle:
                        logger.warning("未找到MicroMsg.db，使用基础缓存")
                        return

                    # 查询联系人信息
                    sql = "SELECT UserName, NickName, Remark FROM Contact LIMIT 1000;"
                    response = requests.post(
                        f"{WECHAT_RPC_HOST}/api/db/execSql",
                        headers={"X-WeChat-PID": WECHAT_PID},
                        json={"dbHandle": micromsg_handle, "sql": sql}
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("Code") == 1 and result.get("Data"):
                            for contact in result["Data"]:
                                username = contact[0]
                                nickname = contact[1] if contact[1] else ""
                                remark = contact[2] if contact[2] else ""

                                # 优先使用备注，其次昵称
                                display_name = remark if remark else nickname if nickname else username
                                self.contacts_cache[username] = display_name

                            logger.info(f"加载了 {len(self.contacts_cache)} 个联系人")
                        else:
                            logger.warning("联系人查询结果为空")
                    else:
                        logger.warning("联系人查询请求失败")

        except Exception as e:
            logger.warning(f"加载联系人异常: {e}")

    def get_contact_name(self, wxid):
        """获取联系人显示名称"""
        if wxid in self.contacts_cache:
            return self.contacts_cache[wxid]

        # 如果缓存中没有，返回微信ID
        return wxid

    def init_monitoring(self):
        """初始化监控起点 - 设置为当前最新消息ID，只监控实时消息"""
        try:
            sql = "SELECT MAX(localId) FROM MSG WHERE StrTalker LIKE '%@chatroom';"
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1 and result.get("Data") and result["Data"][0][0] is not None:
                    try:
                        # 设置为当前最新消息ID，这样只会监控之后的新消息
                        max_id = result["Data"][0][0]
                        if isinstance(max_id, str) and max_id.isdigit():
                            self.last_msg_id = int(max_id)
                        elif isinstance(max_id, (int, float)):
                            self.last_msg_id = int(max_id)
                        else:
                            # 如果无法解析，设置为0
                            self.last_msg_id = 0
                            logger.warning(f"无法解析最大消息ID: {max_id}，从0开始监控")

                        logger.info(f"监控起点设置为当前最新消息ID: {self.last_msg_id}，只监控实时消息")
                        print(f"监控起点设置为: {self.last_msg_id}，只监控实时消息")
                    except (ValueError, TypeError) as e:
                        self.last_msg_id = 0
                        logger.warning(f"解析最大消息ID失败: {e}，从0开始监控")
                        print("解析最大消息ID失败，从0开始监控")
                else:
                    self.last_msg_id = 0
                    logger.info("未找到历史消息，从0开始监控")
                    print("未找到历史消息，从0开始监控")
            else:
                logger.error("初始化监控起点失败")
                self.last_msg_id = 0

        except Exception as e:
            logger.error(f"初始化监控起点异常: {e}")
            self.last_msg_id = 0

    def send_wechat_message(self, wxid, message):
        """发送微信消息"""
        try:
            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/msg/sendTextMsg",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"wxid": wxid, "msg": message}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1:
                    logger.info(f"消息发送成功到 {wxid}: {message}")
                    return True
                else:
                    logger.error(f"消息发送失败: {result}")
                    return False
            else:
                logger.error(f"发送消息请求失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"发送消息异常: {e}")
            return False

    def preprocess_message(self, content):
        """预处理消息内容，移除@机器人的部分"""
        # 移除@机器人的触发词，只保留实际消息内容
        trigger_word = "@我来收集总结你们的聊天(bot)"

        if trigger_word in content:
            # 移除触发词，保留后面的内容
            processed = content.replace(trigger_word, "").strip()

            # 如果移除触发词后没有内容，返回默认提示
            if not processed:
                return "请总结最近的聊天内容"

            return processed

        # 如果没有触发词，返回原内容（理论上不应该到这里，因为已经通过should_reply筛选）
        return content



    def parse_quote_info(self, compress_content, sub_type):
        """解析引用消息信息 - 提取被引用消息ID"""
        # 检查是否是引用消息
        if not compress_content or str(sub_type) not in ["51", "57"]:
            return None, None

        try:
            import base64
            import re

            # base64解码
            decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')

            # 提取被引用消息的服务器ID (MsgSvrID)
            # 方法1: 查找<svrid>标签
            svrid_matches = re.findall(r'<svrid>(\d+)</svrid>', decoded)
            if not svrid_matches:
                # 方法2: 查找长数字ID（15-20位）
                svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
            quote_id = svrid_matches[0] if svrid_matches else None

            # 返回引用信息
            quote_info = {"被引用消息ID": quote_id} if quote_id else None
            return quote_info, None

        except Exception as e:
            logger.debug(f"解析引用消息失败: {e}")
            return None, None

    def call_coze_api(self, message):
        """调用Coze API获取回复"""
        try:
            headers = {
                "Authorization": f"Bearer {COZE_TOKEN}",
                "Content-Type": "application/json"
            }

            data = {
                "bot_id": COZE_BOT_ID,
                "user": "wechat_user",
                "query": message,
                "stream": False
            }

            # 创建一个session并设置代理为None
            session = requests.Session()
            session.trust_env = False  # 忽略环境变量中的代理设置

            response = session.post(
                "https://api.coze.cn/open_api/v2/chat",
                headers=headers,
                json=data,
                verify=False,
                timeout=15,
                proxies={"http": None, "https": None}  # 明确禁用代理
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    # 检查messages是否在根级别
                    messages = result.get("messages", [])
                    if not messages and result.get("data"):
                        # 如果messages在data字段下
                        messages = result["data"].get("messages", [])

                    for msg in messages:
                        if msg.get("type") == "answer":
                            return msg.get("content", "")

                logger.warning(f"Coze API返回异常: {result}")
                return "抱歉，我现在无法回复您的消息。"
            else:
                logger.error(f"Coze API请求失败: {response.status_code}")
                return "抱歉，我现在无法回复您的消息。"

        except Exception as e:
            logger.error(f"调用Coze API异常: {e}")
            return "抱歉，我现在无法回复您的消息。"

    def extract_sender_wxid(self, bytes_extra):
        """从BytesExtra中提取发送者微信ID"""
        if not bytes_extra:
            return None

        try:
            bytes_str = str(bytes_extra)

            # 尝试base64解码
            try:
                decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
                wxid_pattern = r'wxid_[a-zA-Z0-9]+'
                match = re.search(wxid_pattern, decoded)
                if match:
                    return match.group(0)
            except:
                pass

            # 直接查找
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, bytes_str)
            if match:
                return match.group(0)

            return None
        except:
            return None

    def process_message(self, msg_data):
        """处理单条消息"""
        try:
            # 解析消息数据 - 按照SQL查询顺序
            local_id = msg_data[0]
            talker_id = msg_data[1]  # TalkerId
            create_time = msg_data[2]
            str_talker = msg_data[3]  # 群ID
            str_content = msg_data[4]  # 消息内容
            is_sender = msg_data[5]  # 是否自己发送
            msg_type = msg_data[6]  # 消息类型
            sub_type = msg_data[7]  # 子类型
            bytes_extra = msg_data[8]  # 发送者信息
            compress_content = msg_data[9]  # 压缩内容（引用信息）
            msg_svr_id = msg_data[10]  # 服务器消息ID

            # 跳过自己发送的消息
            if is_sender == 1:
                return

            # 提取发送者微信ID
            sender_wxid = self.extract_sender_wxid(bytes_extra)
            if not sender_wxid:
                return

            # 获取发送者昵称
            sender_name = self.get_contact_name(sender_wxid)

            # 获取群名称
            group_name = self.get_contact_name(str_talker)

            # 格式化时间
            try:
                # 确保create_time是数字类型
                if isinstance(create_time, str):
                    create_time = int(create_time)
                formatted_time = datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError, OSError):
                formatted_time = "未知时间"

            # 处理消息ID - 优先使用MsgSvrID
            if msg_svr_id and str(msg_svr_id) != "0":
                message_id = str(msg_svr_id)
            else:
                message_id = f"local_{local_id}"

            # 处理消息内容 - 统一使用StrContent字段（标准消息和引用消息完全相同的处理方式）
            actual_content = str_content
            quote_info = None

            # 如果是引用消息，仅解析引用信息（不影响消息内容获取）
            if str(msg_type) == "49" and str(sub_type) in ["51", "57"]:
                quote_info, _ = self.parse_quote_info(compress_content, sub_type)

            # 构建消息对象 - 只包含需要的字段
            message_obj = {
                "发送人": sender_name,
                "发送时间": formatted_time,
                "群聊名称": group_name,
                "消息内容": actual_content,
                "消息类型": str(msg_type),
                "消息ID": message_id
            }

            # 添加引用信息
            if quote_info:
                message_obj["引用信息"] = quote_info

            # 为内部处理保留额外字段
            message_obj["群ID"] = str_talker
            message_obj["发送者ID"] = sender_wxid

            # 构建输出对象 - 只包含需要显示的字段
            output_obj = {
                "发送人": message_obj["发送人"],
                "发送时间": message_obj["发送时间"],
                "群聊名称": message_obj["群聊名称"],
                "消息内容": message_obj["消息内容"],
                "消息类型": message_obj["消息类型"],
                "消息ID": message_obj["消息ID"]
            }

            # 如果有引用信息，直接添加被引用消息ID字段
            if "引用信息" in message_obj and message_obj["引用信息"]:
                quote_info = message_obj["引用信息"]
                if "被引用消息ID" in quote_info:
                    output_obj["被引用消息ID"] = quote_info["被引用消息ID"]

            # 输出消息JSON
            print(json.dumps(output_obj, ensure_ascii=False, indent=2))

            # 如果是测试群或者包含特定触发词，进行回复
            if self.should_reply(message_obj):
                self.handle_reply(message_obj)

        except Exception as e:
            logger.error(f"处理消息异常: {e}")

    def should_reply(self, message_obj):
        """判断是否应该回复消息"""
        # 只有当消息内容包含特定触发词时才回复
        content = message_obj.get("消息内容", "")

        # 只有当消息内容包含特定触发词时才回复
        if "@我来收集总结你们的聊天(bot)" in content:
            return True

        return False

    def handle_reply(self, message_obj):
        """处理回复逻辑"""
        content = message_obj.get("消息内容", "")
        group_id = message_obj.get("群ID", "")
        sender_name = message_obj.get("发送人", "")
        group_name = message_obj.get("群聊名称", "")

        print(f"🎯 触发回复 - 群聊: {group_name}, 发送人: {sender_name}")
        logger.info(f"触发回复 - 群聊: {group_name}, 发送人: {sender_name}, 消息: {content}")

        # 如果消息为空，使用默认回复
        if not content:
            reply = f"你好 {sender_name}，我是小夕智能助手！"
        else:
            # 预处理消息内容：移除@机器人的部分，只保留实际消息内容
            processed_content = self.preprocess_message(content)
            print(f"📝 处理后消息: {processed_content}")

            # 调用Coze API获取智能回复
            print("🤖 正在调用Coze API...")
            reply = self.call_coze_api(processed_content)

        # 发送回复
        print(f"📤 发送回复到群聊: {group_name}")
        self.send_wechat_message(group_id, reply)
        print("✅ 回复发送完成\n")

    def monitor_messages(self):
        """监控新消息"""
        try:
            sql = f"""
            SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
            FROM MSG
            WHERE localId > {self.last_msg_id}
            AND StrTalker LIKE '%@chatroom'
            ORDER BY localId ASC;
            """

            response = requests.post(
                f"{WECHAT_RPC_HOST}/api/db/execSql",
                headers={"X-WeChat-PID": WECHAT_PID},
                json={"dbHandle": self.db_handle, "sql": sql}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("Code") == 1 and result.get("Data"):
                    # 如果是第一次运行，跳过所有历史消息，只更新last_msg_id
                    if self.is_first_run:
                        print(f"📝 跳过 {len(result['Data'])} 条历史消息，开始监控实时消息")
                        logger.info(f"跳过 {len(result['Data'])} 条历史消息，只监控实时消息")
                        # 只更新last_msg_id到最新消息，不处理消息内容
                        if result["Data"]:
                            self.last_msg_id = result["Data"][-1][0]  # 取最后一条消息的ID
                        self.is_first_run = False
                    else:
                        # 正常处理新消息
                        for msg_data in result["Data"]:
                            self.process_message(msg_data)
                            # 更新最后处理的消息ID
                            self.last_msg_id = msg_data[0]

        except Exception as e:
            logger.error(f"监控消息异常: {e}")

    def start(self):
        """启动监控"""
        print("\n🚀 正在启动系统...")
        logger.info("启动微信群消息监控与Coze智能回复系统...")

        # 1. 获取数据库句柄
        print("📊 正在连接微信数据库...")
        if not self.get_db_handle():
            print("❌ 数据库连接失败，程序退出")
            logger.error("无法获取数据库句柄，程序退出")
            return

        # 2. 加载联系人信息
        print("👥 正在加载联系人信息...")
        self.load_contacts()

        # 3. 初始化监控起点
        print("⚙️  正在初始化监控起点...")
        self.init_monitoring()

        # 4. 启动完成
        print("✅ 系统启动完成！")
        print("\n📱 开始监控微信群消息...")
        print("💡 发送包含 '@我来收集总结你们的聊天(bot)' 的消息来触发回复")
        print("⏹️  按 Ctrl+C 停止监控\n")
        logger.info("开始监控群消息...")

        try:
            while True:
                self.monitor_messages()
                time.sleep(3)  # 每3秒检查一次新消息

        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
            logger.info("用户中断，程序退出")
        except Exception as e:
            print(f"\n❌ 监控异常: {e}")
            logger.error(f"监控循环异常: {e}")

def main():
    """主函数"""
    print("🤖 微信群消息监控与Coze智能回复系统")
    print("=" * 60)
    print("功能说明：")
    print("  ✅ 监控微信群实时消息")
    print("  ✅ 触发词：@我来收集总结你们的聊天(bot)")
    print("  ✅ 自动调用Coze API生成智能回复")
    print("  ✅ 消息预处理：移除@机器人部分，避免干扰")
    print("=" * 60)

    try:
        bot = WeChatCozeBot()
        bot.start()
    except KeyboardInterrupt:
        print("\n👋 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    main()