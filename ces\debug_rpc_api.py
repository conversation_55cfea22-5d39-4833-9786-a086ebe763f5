#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试RPC API返回的消息数据结构
"""

import requests
import json

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"

def debug_rpc_messages():
    """调试RPC API返回的消息数据"""
    
    # 测试群聊ID（从您的输出中获取）
    test_group = "9948090198@chatroom"  # 测试2群
    
    print(f"🔍 调试RPC API消息获取")
    print(f"群聊ID: {test_group}")
    print("=" * 50)
    
    try:
        # 使用getMsgListByPage API
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/msg/getMsgListByPage",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={
                "limit": 10,  # 获取最近10条消息
                "isSortDesc": True,
                "wxid": test_group
            },
            timeout=10,
            verify=False,
            proxies={"http": None, "https": None}
        )
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应结构: Code={result.get('Code')}, Message={result.get('Message')}")
            
            if result.get("Code") == 1 and result.get("Data"):
                messages = result["Data"]
                print(f"📝 获取到 {len(messages)} 条消息")
                print()
                
                for i, msg in enumerate(messages):
                    print(f"=== 消息 {i+1} ===")
                    print(f"消息类型: Type={msg.get('Type')}, SubType={msg.get('SubType')}")
                    print(f"MsgSvrID: {msg.get('MsgSvrID')}")
                    print(f"StrContent: '{msg.get('StrContent')}' (长度: {len(str(msg.get('StrContent', '')))}")
                    print(f"IsSender: {msg.get('IsSender')}")
                    print(f"CreateTime: {msg.get('CreateTime')}")
                    
                    # 显示所有字段
                    print("所有字段:")
                    for key, value in msg.items():
                        if value:  # 只显示非空字段
                            print(f"  {key}: {value}")
                    print("-" * 30)
                    
            else:
                print(f"❌ API返回错误: {result}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")

    print("\n" + "=" * 50)
    
    # 测试getMsgList API
    print("🔍 测试getMsgList API")
    try:
        from datetime import datetime, timedelta
        
        # 获取今天的日期范围
        today = datetime.now()
        start_date = today.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/msg/getMsgList",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={
                "wxid": test_group,
                "startDate": start_date,
                "endDate": end_date,
                "dateType": 2
            },
            timeout=10,
            verify=False,
            proxies={"http": None, "https": None}
        )
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应结构: Code={result.get('Code')}, Message={result.get('Message')}")
            
            if result.get("Code") == 1 and result.get("Data"):
                messages = result["Data"]
                print(f"📝 获取到 {len(messages)} 条消息")
                
                # 只显示最近3条消息的详细信息
                for i, msg in enumerate(messages[-3:]):
                    print(f"=== 最近消息 {i+1} ===")
                    print(f"消息类型: Type={msg.get('Type')}, SubType={msg.get('SubType')}")
                    print(f"MsgSvrID: {msg.get('MsgSvrID')}")
                    print(f"StrContent: '{msg.get('StrContent')}' (长度: {len(str(msg.get('StrContent', '')))})")
                    print("-" * 30)
            else:
                print(f"❌ API返回错误: {result}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ getMsgList调试异常: {e}")

if __name__ == "__main__":
    debug_rpc_messages()
