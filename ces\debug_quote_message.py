#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引用消息调试脚本 - 深度分析引用消息的数据结构
"""

import requests
import json
import base64
import re

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"

def get_db_handle():
    """获取数据库句柄"""
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={}
        )

        if response.status_code == 200:
            data = response.json()
            if data.get("Code") == 1 and data.get("Data"):
                for db_info in data["Data"]:
                    if "MSG0.db" in db_info.get("databaseName", ""):
                        return str(db_info["handle"])
        return None
    except Exception as e:
        print(f"获取数据库句柄失败: {e}")
        return None

def analyze_quote_messages():
    """分析最近的引用消息"""
    db_handle = get_db_handle()
    if not db_handle:
        print("❌ 无法获取数据库句柄")
        return

    print(f"✅ 获取到数据库句柄: {db_handle}")

    # 查询最近的引用消息（Type=49）
    sql = """
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
    FROM MSG 
    WHERE Type = 49 
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId DESC 
    LIMIT 5;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                print(f"\n📊 找到 {len(result['Data'])} 条引用消息，开始分析：\n")
                
                for i, msg_data in enumerate(result["Data"]):
                    print(f"=== 引用消息 {i+1} ===")
                    print(f"localId: {msg_data[0]}")
                    print(f"StrContent: '{msg_data[4]}' (长度: {len(str(msg_data[4])) if msg_data[4] else 0})")
                    print(f"Type: {msg_data[6]}, SubType: {msg_data[7]}")
                    print(f"MsgSvrID: {msg_data[10]}")
                    
                    # 分析CompressContent
                    compress_content = msg_data[9]
                    if compress_content:
                        try:
                            decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
                            print(f"CompressContent解码内容:")
                            print(f"原始长度: {len(str(compress_content))}")
                            print(f"解码长度: {len(decoded)}")
                            # 查找可能包含用户输入内容的字段
                            print("🔍 查找用户输入内容:")

                            # 方法1: 查找title字段
                            title_match = re.search(r'<title[^>]*><!\[CDATA\[(.*?)\]\]></title>', decoded, re.DOTALL)
                            if title_match:
                                print(f"  title (CDATA): '{title_match.group(1)}'")

                            # 方法2: 查找content字段
                            content_match = re.search(r'<content[^>]*><!\[CDATA\[(.*?)\]\]></content>', decoded, re.DOTALL)
                            if content_match:
                                print(f"  content (CDATA): '{content_match.group(1)}'")

                            # 方法3: 查找displayname字段
                            display_match = re.search(r'<displayname[^>]*><!\[CDATA\[(.*?)\]\]></displayname>', decoded, re.DOTALL)
                            if display_match:
                                print(f"  displayname (CDATA): '{display_match.group(1)}'")

                            # 方法4: 查找所有CDATA内容
                            cdata_matches = re.findall(r'<!\[CDATA\[(.*?)\]\]>', decoded, re.DOTALL)
                            if cdata_matches:
                                print(f"  所有CDATA内容: {cdata_matches}")

                            print(f"  完整XML内容: {decoded}")
                            print()
                            
                        except Exception as e:
                            print(f"CompressContent解析失败: {e}")
                    else:
                        print("CompressContent为空")
                    
                    print("-" * 50)
            else:
                print("❌ 未找到引用消息数据")
        else:
            print(f"❌ 查询失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 分析异常: {e}")

if __name__ == "__main__":
    print("🔍 引用消息数据结构分析工具")
    print("=" * 50)
    analyze_quote_messages()
