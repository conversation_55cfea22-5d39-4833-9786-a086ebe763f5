#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的引用消息调试脚本
"""

import requests
import json
import base64
import re

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"

def get_db_handle():
    """获取数据库句柄"""
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={}
        )

        if response.status_code == 200:
            data = response.json()
            if data.get("Code") == 1 and data.get("Data"):
                for db_info in data["Data"]:
                    if "MSG0.db" in db_info.get("databaseName", ""):
                        return str(db_info["handle"])
        return None
    except Exception as e:
        print(f"获取数据库句柄失败: {e}")
        return None

def analyze_latest_quote():
    """分析最新的引用消息"""
    db_handle = get_db_handle()
    if not db_handle:
        print("❌ 无法获取数据库句柄")
        return

    # 查询最新的一条引用消息
    sql = """
    SELECT StrContent, CompressContent, Type, SubType
    FROM MSG 
    WHERE Type = 49 
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId DESC 
    LIMIT 1;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                msg_data = result["Data"][0]
                str_content = msg_data[0]
                compress_content = msg_data[1]
                msg_type = msg_data[2]
                sub_type = msg_data[3]
                
                print(f"StrContent: '{str_content}' (长度: {len(str(str_content)) if str_content else 0})")
                print(f"Type: {msg_type}, SubType: {sub_type}")
                
                if compress_content:
                    try:
                        decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
                        print(f"\n完整XML内容:")
                        print(decoded)
                        print("\n" + "="*50)
                        
                        # 查找可能的内容字段
                        patterns = [
                            r'<title[^>]*><!\[CDATA\[(.*?)\]\]></title>',
                            r'<content[^>]*><!\[CDATA\[(.*?)\]\]></content>',
                            r'<displayname[^>]*><!\[CDATA\[(.*?)\]\]></displayname>',
                        ]
                        
                        for pattern in patterns:
                            matches = re.findall(pattern, decoded, re.DOTALL)
                            if matches:
                                print(f"找到内容: {matches}")
                                
                    except Exception as e:
                        print(f"解析失败: {e}")
                        
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == "__main__":
    analyze_latest_quote()
