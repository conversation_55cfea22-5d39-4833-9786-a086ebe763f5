#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的引用消息分析 - 查询最新的引用消息
"""

import requests
import json

# 配置信息
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "17576"

def get_db_handle():
    """获取数据库句柄"""
    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/getDBInfo",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={}
        )

        if response.status_code == 200:
            data = response.json()
            if data.get("Code") == 1 and data.get("Data"):
                for db_info in data["Data"]:
                    if "MSG0.db" in db_info.get("databaseName", ""):
                        return str(db_info["handle"])
        return None
    except Exception as e:
        print(f"获取数据库句柄失败: {e}")
        return None

def analyze_latest_quote():
    """分析最新的引用消息"""
    db_handle = get_db_handle()
    if not db_handle:
        print("❌ 无法获取数据库句柄")
        return

    print(f"✅ 获取到数据库句柄: {db_handle}")

    # 查询最新的引用消息，包含DisplayContent字段
    sql = """
    SELECT localId, StrTalker, StrContent, DisplayContent, Type, SubType, MsgSvrID
    FROM MSG 
    WHERE Type = 49 
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId DESC 
    LIMIT 3;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql}
        )

        if response.status_code == 200:
            result = response.json()
            print(f"📊 查询结果: Code={result.get('Code')}, Message={result.get('Message')}")
            
            if result.get("Code") == 1 and result.get("Data"):
                messages = result["Data"]
                print(f"📝 找到 {len(messages)} 条引用消息")
                
                for i, msg_data in enumerate(messages):
                    print(f"\n=== 引用消息 {i+1} ===")
                    print(f"localId: {msg_data[0]}")
                    print(f"StrTalker: {msg_data[1]}")
                    print(f"StrContent: '{msg_data[2]}' (长度: {len(str(msg_data[2])) if msg_data[2] else 0})")
                    print(f"DisplayContent: '{msg_data[3]}' (长度: {len(str(msg_data[3])) if msg_data[3] else 0})")
                    print(f"Type: {msg_data[4]}, SubType: {msg_data[5]}")
                    print(f"MsgSvrID: {msg_data[6]}")
                    
                    # 如果DisplayContent不为空，这可能就是我们要找的内容
                    if msg_data[3] and str(msg_data[3]).strip():
                        print(f"🎯 DisplayContent可能包含用户输入: '{msg_data[3]}'")
                    
                    print("-" * 40)
                    
            else:
                print(f"❌ 查询失败: {result}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 查询异常: {e}")

    # 对比查询最新的标准消息
    print(f"\n📋 对比：最新的标准消息")
    sql_normal = """
    SELECT localId, StrTalker, StrContent, DisplayContent, Type, SubType, MsgSvrID
    FROM MSG 
    WHERE Type = 1 
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId DESC 
    LIMIT 2;
    """

    try:
        response = requests.post(
            f"{WECHAT_RPC_HOST}/api/db/execSql",
            headers={"X-WeChat-PID": WECHAT_PID},
            json={"dbHandle": db_handle, "sql": sql_normal}
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("Code") == 1 and result.get("Data"):
                messages = result["Data"]
                print(f"📝 找到 {len(messages)} 条标准消息")
                
                for i, msg_data in enumerate(messages):
                    print(f"\n=== 标准消息 {i+1} ===")
                    print(f"StrContent: '{msg_data[2]}' (长度: {len(str(msg_data[2])) if msg_data[2] else 0})")
                    print(f"DisplayContent: '{msg_data[3]}' (长度: {len(str(msg_data[3])) if msg_data[3] else 0})")
                    print(f"MsgSvrID: {msg_data[6]}")
                    
    except Exception as e:
        print(f"❌ 对比查询异常: {e}")

if __name__ == "__main__":
    analyze_latest_quote()
